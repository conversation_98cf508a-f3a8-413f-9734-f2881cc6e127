buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
        // Monorepo için node_modules yolu
        nodeModulesPath = "$rootDir/../../../node_modules"
        reactNativeVersion = "0.79.4"
    }
    repositories {
        google()
        mavenCentral()
        mavenLocal()
        maven { url("https://repo1.maven.org/maven2") }
        maven { url("https://www.jitpack.io") }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.2")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        mavenLocal()

        // React Native 0.74.4 için Maven Central repository
        maven {
            url "https://repo1.maven.org/maven2"
            name "Maven Central"
        }

        // Mapbox Maven repository
        maven {
            url 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication { basic(BasicAuthentication) }
            credentials {
                username = 'mapbox'
                password = System.getenv('MAPBOX_DOWNLOADS_TOKEN') ?: project.properties['MAPBOX_DOWNLOADS_TOKEN'] ?: ""
            }
        }

        // JitPack repository
        maven { url("https://www.jitpack.io") }

        // Android JSC repository
        maven { url("https://android-sdk.is.com/") }
    }
    
    // Global configuration for all projects
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion
                
                // BuildConfig özelliğini etkinleştir
                buildFeatures {
                    buildConfig true
                }
                
                // CompileOptions ayarla
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
                
                // DefaultConfig ayarla
                if (project.android.hasProperty('defaultConfig')) {
                    project.android.defaultConfig {
                        minSdkVersion rootProject.ext.minSdkVersion
                        targetSdkVersion rootProject.ext.targetSdkVersion
                    }
                }
                
                // React Native 0.74.4 dependency resolution
                configurations.all {
                    resolutionStrategy {
                        force "com.facebook.react:react-native:${rootProject.ext.reactNativeVersion}"
                        force "com.facebook.react:react-android:${rootProject.ext.reactNativeVersion}"
                        force "com.facebook.react:hermes-android:${rootProject.ext.reactNativeVersion}"

                        // AsyncStorage v2+ için uyumluluk
                        force "androidx.lifecycle:lifecycle-runtime:2.6.2"
                        force "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
                    }
                }
                
                // Namespace ayarla
                if (!project.android.hasProperty('namespace') || project.android.namespace == null) {
                    def manifestFile = project.file('src/main/AndroidManifest.xml')
                    if (manifestFile.exists()) {
                        def manifestText = manifestFile.text
                        def packageMatch = manifestText =~ /package\s*=\s*"([^"]+)"/
                        if (packageMatch) {
                            project.android.namespace = packageMatch[0][1]
                        }
                    }
                    
                    // React Native paketleri için özel namespace'ler
                    switch (project.name) {
                        case 'react-native-linear-gradient':
                            project.android.namespace = 'com.BV.LinearGradient'
                            break
                        case 'react-native-gesture-handler':
                            project.android.namespace = 'com.swmansion.gesturehandler'
                            break
                        case 'react-native-reanimated':
                            project.android.namespace = 'com.swmansion.reanimated'
                            break
                        case 'react-native-safe-area-context':
                            project.android.namespace = 'com.th3rdwave.safeareacontext'
                            break
                        case { it.contains('react-native-async-storage') }:
                            project.android.namespace = 'com.reactnativecommunity.asyncstorage'
                            break
                        case 'react-native-screens':
                            project.android.namespace = 'com.swmansion.rnscreens'
                            break
                        case 'react-native-vector-icons':
                            project.android.namespace = 'com.oblador.vectoricons'
                            break
                        case 'react-native-haptic-feedback':
                            project.android.namespace = 'com.mkuczera'
                            break
                    }
                }
            }
        }
    }
}

// rnmapbox_maps için özel konfigürasyon
subprojects { project ->
    if (project.name == "rnmapbox_maps") {
        project.afterEvaluate {
            def androidExt = project.extensions.findByName('android')
            if (androidExt != null) {
                androidExt.compileSdkVersion = 35
                androidExt.defaultConfig.targetSdkVersion 35
            }
            project.dependencies.add("implementation", "androidx.lifecycle:lifecycle-runtime:2.6.2")
            project.dependencies.add("implementation", "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2")
            project.dependencies.add("api", "androidx.lifecycle:lifecycle-runtime:2.6.2")
            project.dependencies.add("api", "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2")
        }
    }
}

// Gradle cache'i temizle ve yeniden indir
task cleanCache {
    doLast {
        delete "${gradle.gradleUserHomeDir}/caches"
    }
}

